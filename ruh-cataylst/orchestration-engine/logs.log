2025-06-26 15:53:56 - Main - INFO - Starting Server
2025-06-26 15:53:56 - Main - INFO - Connection at: **************:9092
2025-06-26 15:53:56 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-26 15:53:56 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-26 15:53:56 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-26 15:53:56 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-26 15:53:56 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-26 15:53:58 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-26 15:53:58 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-26 15:53:59 - <PERSON><PERSON><PERSON><PERSON><PERSON> - INFO - Successfully connected to Redis on DB index: 6!
2025-06-26 15:54:01 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-26 15:54:01 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-26 15:54:04 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-26 15:54:04 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-26 15:54:04 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-26 15:54:06 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-26 15:54:06 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-26 15:54:07 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-26 15:54:07 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-26 15:54:07 - RedisEventListener - INFO - Redis event listener started
2025-06-26 15:54:07 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-26 15:54:07 - StateManager - DEBUG - Using provided database connections
2025-06-26 15:54:07 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 15:54:07 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 15:54:07 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 15:54:08 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-26 15:54:08 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 15:54:08 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 15:54:08 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-26 15:54:08 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-26 15:54:08 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-26 15:54:08 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-26 15:54:11 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-26 15:54:11 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-26 15:54:11 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-26 15:54:16 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-26 15:54:22 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-26 15:54:22 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-26 15:54:22 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-26 15:54:28 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-26 15:54:28 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-26 15:54:28 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-26 15:54:34 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-26 15:54:34 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-26 15:55:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 15:55:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:55:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:55:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 15:55:40 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=949
2025-06-26 15:55:40 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1750933540, 'task_type': 'workflow', 'data': {'workflow_id': 'b3b7f168-29ee-4e51-8717-16f4c8beb832', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["ab","cd","ef"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-26 15:55:40 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: b3b7f168-29ee-4e51-8717-16f4c8beb832
2025-06-26 15:55:40 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/b3b7f168-29ee-4e51-8717-16f4c8beb832
2025-06-26 15:55:44 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-26 15:55:44 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "b3b7f168-29ee-4e51-8717-16f4c8beb832",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/eebb40ff-8e31-4edd-b811-6ebd6e6f88b1.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/f6dc7671-f9af-4bd5-8964-801acd310e8e.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-26T08:07:41.710172",
    "updated_at": "2025-06-26T10:25:19.439371",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-*************"
      }
    ],
    "is_updated": true
  }
}
2025-06-26 15:55:44 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for b3b7f168-29ee-4e51-8717-16f4c8beb832 - server_script_path is optional
2025-06-26 15:55:44 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-26 15:55:44 - StateManager - DEBUG - Using global database connections from initializer
2025-06-26 15:55:44 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 15:55:44 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 15:55:44 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 15:55:45 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 15:55:45 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 15:55:45 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-26 15:55:45 - StateManager - DEBUG - Using provided database connections
2025-06-26 15:55:45 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 15:55:45 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 15:55:45 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 15:55:46 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 15:55:46 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 15:55:46 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-26 15:55:46 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-*************: ['transition-LoopNode-*************']
2025-06-26 15:55:46 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-26 15:55:46 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-26 15:55:46 - StateManager - DEBUG - Transition transition-MergeDataComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-26 15:55:46 - MCPToolExecutor - DEBUG - Set correlation ID to: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f
2025-06-26 15:55:46 - EnhancedWorkflowEngine - DEBUG - Set correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f in tool_executor
2025-06-26 15:55:46 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-26 15:55:46 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-26 15:55:46 - NodeExecutor - DEBUG - Set correlation ID to: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f
2025-06-26 15:55:46 - EnhancedWorkflowEngine - DEBUG - Set correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f in node_executor
2025-06-26 15:55:46 - AgentExecutor - DEBUG - Set correlation ID to: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f
2025-06-26 15:55:46 - EnhancedWorkflowEngine - DEBUG - Set correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f in agent_executor
2025-06-26 15:55:46 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-26 15:55:46 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-26 15:55:46 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-26 15:55:46 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f
2025-06-26 15:55:46 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f
2025-06-26 15:55:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-26 15:55:46 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-26 15:55:46 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-26 15:55:46 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-26 15:55:46 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-26 15:55:47 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:d019763e-ca13-45c2-a8a0-f5c4ee37dc8f'
2025-06-26 15:55:47 - RedisManager - DEBUG - Set key 'workflow_state:d019763e-ca13-45c2-a8a0-f5c4ee37dc8f' with TTL of 600 seconds
2025-06-26 15:55:47 - StateManager - INFO - Workflow state saved to Redis for workflow ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:55:47 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-26 15:55:47 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-26 15:55:47 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-26 15:55:47 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-26 15:55:47 - StateManager - INFO - Terminated: False
2025-06-26 15:55:47 - StateManager - INFO - Pending transitions (0): []
2025-06-26 15:55:47 - StateManager - INFO - Waiting transitions (0): []
2025-06-26 15:55:47 - StateManager - INFO - Completed transitions (0): []
2025-06-26 15:55:47 - StateManager - INFO - Results stored for 0 transitions
2025-06-26 15:55:47 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 15:55:47 - StateManager - INFO - Workflow status: inactive
2025-06-26 15:55:47 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 15:55:47 - StateManager - INFO - Workflow status: inactive
2025-06-26 15:55:47 - StateManager - INFO - Workflow paused: False
2025-06-26 15:55:47 - StateManager - INFO - ==============================
2025-06-26 15:55:47 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-26 15:55:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:55:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-26 15:55:47 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-26 15:55:47 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-26 15:55:47 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["ab","cd","ef"]', 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-26 15:55:47 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["ab","cd","ef"]', 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-26 15:55:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:55:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-26 15:55:47 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-26 15:55:47 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-26 15:55:47 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-26 15:55:47 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: ab
2025-06-26 15:55:47 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-26 15:55:48 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-26 15:55:48 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:55:48 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-26 15:55:48 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-26 15:55:48 - StateManager - DEBUG - Stored result for transition current_iteration in memory: ab
2025-06-26 15:55:48 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 15:55:49 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 15:55:49 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:55:49 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 15:55:49 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-26 15:55:49 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'ab', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 175557.32731125, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:55:49 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 15:55:50 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 15:55:50 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:55:50 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:55:50 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 15:55:50 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 15:55:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:55:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-26 15:55:50 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 15:55:50 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 15:55:50 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 15:55:50 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'ab', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 175557.32731125, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 15:55:50 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 15:55:50 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'ab', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 175557.32731125, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': ab
2025-06-26 15:55:50 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 15:55:50 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 15:55:50 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 15:55:50 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 15:55:50 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'ab', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:55:50 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'ab', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:55:50 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'ab', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:55:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:55:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-26 15:55:50 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 242de35d-a93e-414e-af6e-2731d6534c80) using provided producer.
2025-06-26 15:55:50 - NodeExecutor - DEBUG - Added correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f to payload
2025-06-26 15:55:50 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'ab', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': '242de35d-a93e-414e-af6e-2731d6534c80', 'correlation_id': 'd019763e-ca13-45c2-a8a0-f5c4ee37dc8f'}
2025-06-26 15:55:50 - NodeExecutor - DEBUG - Request 242de35d-a93e-414e-af6e-2731d6534c80 sent successfully using provided producer.
2025-06-26 15:55:50 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 242de35d-a93e-414e-af6e-2731d6534c80...
2025-06-26 15:55:50 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 949, corr_id: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f
2025-06-26 15:55:56 - NodeExecutor - DEBUG - Result consumer received message: Offset=718
2025-06-26 15:55:56 - NodeExecutor - DEBUG - Received valid result for request_id 242de35d-a93e-414e-af6e-2731d6534c80
2025-06-26 15:55:56 - NodeExecutor - INFO - Result received for request 242de35d-a93e-414e-af6e-2731d6534c80.
2025-06-26 15:55:56 - TransitionHandler - INFO - Execution result from Components executor: "ab\nhello"
2025-06-26 15:55:56 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:55:56 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'ab\nhello', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 15:55:56 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'ab\nhello'}, 'status': 'completed', 'timestamp': 1750933556.343157}}
2025-06-26 15:55:56 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 15:55:57 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 15:55:57 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:55:57 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:55:57 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:55:57 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 15:55:57 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 15:55:57 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 7.09 seconds
2025-06-26 15:55:57 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:55:57 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Completed transition in 7.09 seconds', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-26 15:56:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 15:56:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:56:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:56:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 15:56:27 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: cd
2025-06-26 15:56:27 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-26 15:56:28 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-26 15:56:28 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:56:28 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-26 15:56:28 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:56:28 - StateManager - DEBUG - Stored result for transition current_iteration in memory: cd
2025-06-26 15:56:28 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 15:56:29 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 15:56:29 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:56:29 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 15:56:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:56:29 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'cd', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 175597.22041675, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:56:29 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 15:56:29 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 15:56:29 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:56:29 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:56:29 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:56:29 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 15:56:29 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:56:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-26 15:56:29 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 15:56:29 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 15:56:29 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 15:56:29 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 15:56:29 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 15:56:30 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 15:56:30 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 15:56:30 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'cd', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 175597.22041675, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 15:56:30 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 15:56:30 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'cd', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 175597.22041675, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': cd
2025-06-26 15:56:30 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 15:56:30 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 15:56:30 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 15:56:30 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 15:56:30 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 15:56:30 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 15:56:30 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'cd', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:56:30 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'cd', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:56:30 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'cd', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:56:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:56:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-26 15:56:30 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: d4ab7f49-4e2e-482e-bb93-f51adb26e12c) using provided producer.
2025-06-26 15:56:30 - NodeExecutor - DEBUG - Added correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f to payload
2025-06-26 15:56:30 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'cd', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': 'd4ab7f49-4e2e-482e-bb93-f51adb26e12c', 'correlation_id': 'd019763e-ca13-45c2-a8a0-f5c4ee37dc8f'}
2025-06-26 15:56:30 - NodeExecutor - DEBUG - Request d4ab7f49-4e2e-482e-bb93-f51adb26e12c sent successfully using provided producer.
2025-06-26 15:56:30 - NodeExecutor - DEBUG - Waiting indefinitely for result for request d4ab7f49-4e2e-482e-bb93-f51adb26e12c...
2025-06-26 15:56:31 - NodeExecutor - DEBUG - Result consumer received message: Offset=719
2025-06-26 15:56:31 - NodeExecutor - DEBUG - Received valid result for request_id d4ab7f49-4e2e-482e-bb93-f51adb26e12c
2025-06-26 15:56:31 - NodeExecutor - INFO - Result received for request d4ab7f49-4e2e-482e-bb93-f51adb26e12c.
2025-06-26 15:56:31 - TransitionHandler - INFO - Execution result from Components executor: "cd\nhello"
2025-06-26 15:56:31 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:56:31 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'cd\nhello', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 15:56:31 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'cd\nhello'}, 'status': 'completed', 'timestamp': 1750933591.331624}}
2025-06-26 15:56:31 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 15:56:32 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 15:56:32 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:56:32 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:56:32 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:56:32 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 15:56:32 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 15:56:32 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.20 seconds
2025-06-26 15:56:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:56:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Completed transition in 2.20 seconds', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-26 15:57:02 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: ef
2025-06-26 15:57:02 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-26 15:57:03 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-26 15:57:03 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:03 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-26 15:57:03 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:57:03 - StateManager - DEBUG - Stored result for transition current_iteration in memory: ef
2025-06-26 15:57:03 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 15:57:04 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 15:57:04 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:04 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 15:57:04 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:57:04 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'ef', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 175632.20010725, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:57:04 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 15:57:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 15:57:04 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 15:57:04 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:04 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:57:04 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:57:04 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 15:57:04 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:04 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-26 15:57:04 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 15:57:04 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 15:57:04 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 15:57:04 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 15:57:04 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 15:57:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:57:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pmessage', b'__keyspace@6__:*', b'__keyspace@6__:workflow_state:1b9e4e4f-7d25-414b-86af-5783f811fa8f', b'expired']
2025-06-26 15:57:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 15:57:05 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 15:57:05 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 15:57:05 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'ef', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 175632.20010725, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 15:57:05 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 15:57:05 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'ef', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 175632.20010725, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': ef
2025-06-26 15:57:05 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 15:57:05 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 15:57:05 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 15:57:05 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 15:57:05 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 15:57:05 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 15:57:05 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'ef', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:57:05 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'ef', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:57:05 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'ef', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 15:57:05 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:05 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-26 15:57:05 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 5556c116-6f2a-4f1e-aac3-defe73c6d3c3) using provided producer.
2025-06-26 15:57:05 - NodeExecutor - DEBUG - Added correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f to payload
2025-06-26 15:57:05 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'ef', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': '5556c116-6f2a-4f1e-aac3-defe73c6d3c3', 'correlation_id': 'd019763e-ca13-45c2-a8a0-f5c4ee37dc8f'}
2025-06-26 15:57:05 - NodeExecutor - DEBUG - Request 5556c116-6f2a-4f1e-aac3-defe73c6d3c3 sent successfully using provided producer.
2025-06-26 15:57:05 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 5556c116-6f2a-4f1e-aac3-defe73c6d3c3...
2025-06-26 15:57:06 - NodeExecutor - DEBUG - Result consumer received message: Offset=720
2025-06-26 15:57:06 - NodeExecutor - DEBUG - Received valid result for request_id 5556c116-6f2a-4f1e-aac3-defe73c6d3c3
2025-06-26 15:57:06 - NodeExecutor - INFO - Result received for request 5556c116-6f2a-4f1e-aac3-defe73c6d3c3.
2025-06-26 15:57:06 - TransitionHandler - INFO - Execution result from Components executor: "ef\nhello"
2025-06-26 15:57:06 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:06 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'ef\nhello', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 15:57:06 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'ef\nhello'}, 'status': 'completed', 'timestamp': 1750933626.8071291}}
2025-06-26 15:57:07 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 15:57:07 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 15:57:07 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:07 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:57:07 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:57:07 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 15:57:07 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 15:57:07 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.68 seconds
2025-06-26 15:57:07 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:07 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Completed transition in 2.68 seconds', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-26 15:57:37 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-26 15:57:37 - TransitionHandler - INFO - Execution result from loop executor: {
  "success": true,
  "final_results": {
    "strategy": "collect_all",
    "iteration_results": [
      {
        "success": true,
        "iteration_index": 0,
        "results": {
          "transition-CombineTextComponent-*************": {
            "success": false,
            "error": "Transition did not complete within timeout",
            "timeout": true
          }
        }
      },
      {
        "success": true,
        "iteration_index": 1,
        "results": {
          "transition-CombineTextComponent-*************": {
            "success": false,
            "error": "Transition did not complete within timeout",
            "timeout": true
          }
        }
      },
      {
        "success": true,
        "iteration_index": 2,
        "results": {
          "transition-CombineTextComponent-*************": {
            "success": false,
            "error": "Transition did not complete within timeout",
            "timeout": true
          }
        }
      }
    ],
    "total_iterations": 3,
    "successful_iterations": 3
  },
  "iteration_count": 3,
  "total_iterations": 3
}
2025-06-26 15:57:37 - TransitionHandler - INFO - Checking execution result for errors: {
  "success": true,
  "final_results": {
    "strategy": "collect_all",
    "iteration_results": [
      {
        "success": true,
        "iteration_index": 0,
        "results": {
          "transition-CombineTextComponent-*************": {
            "success": false,
            "error": "Transition did not complete within timeout",
            "timeout": true
          }
        }
      },
      {
        "success": true,
        "iteration_index": 1,
        "results": {
          "transition-CombineTextComponent-*************": {
            "success": false,
            "error": "Transition did not complete within timeout",
            "timeout": true
          }
        }
      },
      {
        "success": true,
        "iteration_index": 2,
        "results": {
          "transition-CombineTextComponent-*************": {
            "success": false,
            "error": "Transition did not complete within timeout",
            "timeout": true
          }
        }
      }
    ],
    "total_iterations": 3,
    "successful_iterations": 3
  },
  "iteration_count": 3,
  "total_iterations": 3
}
2025-06-26 15:57:37 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:37 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'result': {'success': True, 'final_results': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'iteration_count': 3, 'total_iterations': 3}, 'status': 'completed', 'sequence': 14, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 15:57:37 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-26 15:57:37 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'success': True, 'final_results': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'iteration_count': 3, 'total_iterations': 3}
2025-06-26 15:57:38 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 15:57:38 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 15:57:38 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:38 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:57:38 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:57:38 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-26 15:57:38 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-************* (has final/aggregated indicators)
2025-06-26 15:57:38 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-26 15:57:38 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'iteration_list': ['ab', 'cd', 'ef'], 'batch_size': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-26 15:57:38 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-26 15:57:38 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-*************', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-*************main_input'}]}}]
2025-06-26 15:57:38 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-26 15:57:38 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-*************' to next transitions
2025-06-26 15:57:38 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-*************']
2025-06-26 15:57:38 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 111.45 seconds
2025-06-26 15:57:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Completed transition in 111.45 seconds', 'status': 'time_logged', 'sequence': 15, 'workflow_status': 'running'}
2025-06-26 15:57:38 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-*************']]
2025-06-26 15:57:38 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-26 15:57:38 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-*************']
2025-06-26 15:57:38 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-************* to pending (all dependencies met)
2025-06-26 15:57:38 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-*************'}
2025-06-26 15:57:39 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:d019763e-ca13-45c2-a8a0-f5c4ee37dc8f'
2025-06-26 15:57:39 - RedisManager - DEBUG - Set key 'workflow_state:d019763e-ca13-45c2-a8a0-f5c4ee37dc8f' with TTL of 600 seconds
2025-06-26 15:57:39 - StateManager - INFO - Workflow state saved to Redis for workflow ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:39 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-26 15:57:39 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-26 15:57:39 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-*************'}
2025-06-26 15:57:39 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-26 15:57:39 - StateManager - INFO - Terminated: False
2025-06-26 15:57:39 - StateManager - INFO - Pending transitions (0): []
2025-06-26 15:57:39 - StateManager - INFO - Waiting transitions (0): []
2025-06-26 15:57:39 - StateManager - INFO - Completed transitions (6): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-26 15:57:39 - StateManager - INFO - Results stored for 6 transitions
2025-06-26 15:57:39 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 15:57:39 - StateManager - INFO - Workflow status: inactive
2025-06-26 15:57:39 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 15:57:39 - StateManager - INFO - Workflow status: inactive
2025-06-26 15:57:39 - StateManager - INFO - Workflow paused: False
2025-06-26 15:57:39 - StateManager - INFO - ==============================
2025-06-26 15:57:39 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-*************
2025-06-26 15:57:39 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-*************', 'status': 'started', 'sequence': 16, 'workflow_status': 'running'}
2025-06-26 15:57:39 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-*************' (type=standard, execution_type=Components)
2025-06-26 15:57:39 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 15:57:39 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-*************
2025-06-26 15:57:39 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 15:57:39 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 15:57:40 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 15:57:40 - StateManager - DEBUG - Extracted results for 4 tools in transition transition-LoopNode-*************
2025-06-26 15:57:40 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'success': True, 'final_results': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'iteration_count': 3, 'total_iterations': 3}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['success', 'final_results', 'iteration_count', 'total_iterations']
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-26 15:57:40 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 15:57:40 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'final_results': {'success': True, 'final_results': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'iteration_count': 3, 'total_iterations': 3}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['success', 'final_results', 'iteration_count', 'total_iterations']
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Path tracking - Found handle 'final_results' directly in source_results
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - ✅ Handle mapping success: final_results → main_input via path 'final_results': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}
2025-06-26 15:57:40 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 15:57:40 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-26 15:57:40 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-26 15:57:40 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 2
2025-06-26 15:57:40 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Overwrite
2025-06-26 15:57:40 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}
2025-06-26 15:57:40 - TransitionHandler - DEBUG - tool Parameters: {'main_input': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}
2025-06-26 15:57:40 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-*************' with parameters: {'main_input': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}
2025-06-26 15:57:40 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:40 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 17, 'workflow_status': 'running'}
2025-06-26 15:57:40 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: cb74dcce-9661-443b-a0e1-1b8a9db605cb) using provided producer.
2025-06-26 15:57:40 - NodeExecutor - DEBUG - Added correlation_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f to payload
2025-06-26 15:57:40 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'main_input': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}, 'request_id': 'cb74dcce-9661-443b-a0e1-1b8a9db605cb', 'correlation_id': 'd019763e-ca13-45c2-a8a0-f5c4ee37dc8f'}
2025-06-26 15:57:40 - NodeExecutor - DEBUG - Request cb74dcce-9661-443b-a0e1-1b8a9db605cb sent successfully using provided producer.
2025-06-26 15:57:40 - NodeExecutor - DEBUG - Waiting indefinitely for result for request cb74dcce-9661-443b-a0e1-1b8a9db605cb...
2025-06-26 15:57:41 - NodeExecutor - DEBUG - Result consumer received message: Offset=721
2025-06-26 15:57:41 - NodeExecutor - DEBUG - Received valid result for request_id cb74dcce-9661-443b-a0e1-1b8a9db605cb
2025-06-26 15:57:41 - NodeExecutor - INFO - Result received for request cb74dcce-9661-443b-a0e1-1b8a9db605cb.
2025-06-26 15:57:41 - TransitionHandler - INFO - Execution result from Components executor: {
  "strategy": "collect_all",
  "iteration_results": [
    {
      "success": true,
      "iteration_index": 0,
      "results": {
        "transition-CombineTextComponent-*************": {
          "success": false,
          "error": "Transition did not complete within timeout",
          "timeout": true
        }
      }
    },
    {
      "success": true,
      "iteration_index": 1,
      "results": {
        "transition-CombineTextComponent-*************": {
          "success": false,
          "error": "Transition did not complete within timeout",
          "timeout": true
        }
      }
    },
    {
      "success": true,
      "iteration_index": 2,
      "results": {
        "transition-CombineTextComponent-*************": {
          "success": false,
          "error": "Transition did not complete within timeout",
          "timeout": true
        }
      }
    }
  ],
  "total_iterations": 3,
  "successful_iterations": 3
}
2025-06-26 15:57:41 - TransitionHandler - INFO - Checking execution result for errors: {
  "strategy": "collect_all",
  "iteration_results": [
    {
      "success": true,
      "iteration_index": 0,
      "results": {
        "transition-CombineTextComponent-*************": {
          "success": false,
          "error": "Transition did not complete within timeout",
          "timeout": true
        }
      }
    },
    {
      "success": true,
      "iteration_index": 1,
      "results": {
        "transition-CombineTextComponent-*************": {
          "success": false,
          "error": "Transition did not complete within timeout",
          "timeout": true
        }
      }
    },
    {
      "success": true,
      "iteration_index": 2,
      "results": {
        "transition-CombineTextComponent-*************": {
          "success": false,
          "error": "Transition did not complete within timeout",
          "timeout": true
        }
      }
    }
  ],
  "total_iterations": 3,
  "successful_iterations": 3
}
2025-06-26 15:57:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}, 'status': 'completed', 'sequence': 18, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 15:57:41 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in memory: {'MergeDataComponent': {'transition_id': 'transition-MergeDataComponent-*************', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': {'result': {'strategy': 'collect_all', 'iteration_results': [{'success': True, 'iteration_index': 0, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 1, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}, {'success': True, 'iteration_index': 2, 'results': {'transition-CombineTextComponent-*************': {'success': False, 'error': 'Transition did not complete within timeout', 'timeout': True}}}], 'total_iterations': 3, 'successful_iterations': 3}}, 'status': 'completed', 'timestamp': 1750933661.210117}}
2025-06-26 15:57:41 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-MergeDataComponent-*************'
2025-06-26 15:57:42 - RedisManager - DEBUG - Set key 'result:transition-MergeDataComponent-*************' with TTL of 300 seconds
2025-06-26 15:57:42 - StateManager - DEBUG - Stored result for transition transition-MergeDataComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 15:57:42 - StateManager - INFO - Marked transition transition-MergeDataComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 15:57:42 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'loop_iteration_2', 'transition-MergeDataComponent-*************', 'loop_iteration_1', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-MergeDataComponent-*************
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'dict'>
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀 Execution result keys: ['strategy', 'iteration_results', 'total_iterations', 'successful_iterations']
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: has_routing_decision=False
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔍 execution_result keys: ['strategy', 'iteration_results', 'total_iterations', 'successful_iterations']
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-MergeDataComponent-*************:
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 15:57:42 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-MergeDataComponent-*************, returning empty list
2025-06-26 15:57:42 - TransitionHandler - INFO - Completed transition transition-MergeDataComponent-************* in 2.18 seconds
2025-06-26 15:57:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id d019763e-ca13-45c2-a8a0-f5c4ee37dc8f):
2025-06-26 15:57:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'result': 'Completed transition in 2.18 seconds', 'status': 'time_logged', 'sequence': 19, 'workflow_status': 'running'}
2025-06-26 15:57:42 - EnhancedWorkflowEngine - DEBUG - Results: [[]]
2025-06-26 15:57:42 - StateManager - INFO - Workflow terminated flag set to: True
2025-06-26 15:57:42 - EnhancedWorkflowEngine - INFO - Workflow is marked as terminated, ending workflow execution.
2025-06-26 15:57:42 - StateManager - DEBUG - Workflow active: False (terminated=True, pending=empty, waiting=empty)
2025-06-26 15:57:42 - EnhancedWorkflowEngine - INFO - Workflow execution completed.
2025-06-26 15:57:42 - KafkaWorkflowConsumer - INFO - Workflow 'b3b7f168-29ee-4e51-8717-16f4c8beb832' final status: completed, result: Workflow 'b3b7f168-29ee-4e51-8717-16f4c8beb832' executed successfully.
2025-06-26 15:57:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f, response: {'status': 'complete', 'result': "Workflow 'b3b7f168-29ee-4e51-8717-16f4c8beb832' executed successfully.", 'workflow_status': 'completed'}
2025-06-26 15:57:42 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: d019763e-ca13-45c2-a8a0-f5c4ee37dc8f 
2025-06-26 15:58:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 15:58:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:58:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:58:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 15:59:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 15:59:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:59:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 15:59:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:00:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:00:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:00:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:00:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:01:04 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:01:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:01:05 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:01:05 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:01:07 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-26 16:01:07 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-26 16:01:07 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-26 16:01:07 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-26 16:01:07 - Main - ERROR - Shutting down due to keyboard interrupt...
