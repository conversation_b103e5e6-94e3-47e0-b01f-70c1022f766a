2025-06-26 16:43:18 - Main - INFO - Starting Server
2025-06-26 16:43:18 - Main - INFO - Connection at: **************:9092
2025-06-26 16:43:18 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-26 16:43:18 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-26 16:43:18 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-26 16:43:18 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-26 16:43:18 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-26 16:43:19 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-26 16:43:19 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-26 16:43:21 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-26 16:43:23 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-26 16:43:23 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-26 16:43:25 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-26 16:43:26 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-26 16:43:26 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-26 16:43:28 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-26 16:43:28 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-26 16:43:30 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-26 16:43:30 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-26 16:43:30 - RedisEventListener - INFO - Redis event listener started
2025-06-26 16:43:30 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-26 16:43:30 - StateManager - DEBUG - Using provided database connections
2025-06-26 16:43:30 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 16:43:30 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 16:43:30 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 16:43:30 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-26 16:43:30 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 16:43:30 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 16:43:30 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-26 16:43:30 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-26 16:43:31 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-26 16:43:31 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-26 16:43:33 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-26 16:43:33 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-26 16:43:33 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-26 16:43:46 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-26 16:43:52 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-26 16:43:52 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-26 16:43:52 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-26 16:43:58 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-26 16:43:58 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-26 16:43:58 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-26 16:44:05 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-26 16:44:05 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-26 16:44:26 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:44:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:44:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:44:27 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:44:45 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=950
2025-06-26 16:44:45 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1750936484, 'task_type': 'workflow', 'data': {'workflow_id': 'b3b7f168-29ee-4e51-8717-16f4c8beb832', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["bc","de","of"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-26 16:44:45 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: b3b7f168-29ee-4e51-8717-16f4c8beb832
2025-06-26 16:44:45 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/b3b7f168-29ee-4e51-8717-16f4c8beb832
2025-06-26 16:44:48 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-26 16:44:48 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "b3b7f168-29ee-4e51-8717-16f4c8beb832",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/1f26d850-71b2-48e5-8e47-ddbed745342f.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a08b2b33-545a-4b20-950a-a65a45067d82.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-26T08:07:41.710172",
    "updated_at": "2025-06-26T11:14:12.174759",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1750925348097"
      }
    ],
    "is_updated": true
  }
}
2025-06-26 16:44:49 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for b3b7f168-29ee-4e51-8717-16f4c8beb832 - server_script_path is optional
2025-06-26 16:44:49 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-26 16:44:49 - StateManager - DEBUG - Using global database connections from initializer
2025-06-26 16:44:49 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 16:44:49 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 16:44:49 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 16:44:50 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 16:44:50 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 16:44:50 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-26 16:44:50 - StateManager - DEBUG - Using provided database connections
2025-06-26 16:44:50 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 16:44:50 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 16:44:50 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 16:44:51 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 16:44:51 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 16:44:51 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1750925348097: ['transition-LoopNode-*************']
2025-06-26 16:44:51 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-26 16:44:51 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-26 16:44:51 - StateManager - DEBUG - Transition transition-MergeDataComponent-1750925348097 depends on: ['transition-LoopNode-*************']
2025-06-26 16:44:51 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-26 16:44:51 - MCPToolExecutor - DEBUG - Set correlation ID to: 496d6e81-a514-452d-8bf5-fb629b599353
2025-06-26 16:44:51 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 496d6e81-a514-452d-8bf5-fb629b599353 in tool_executor
2025-06-26 16:44:51 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-26 16:44:51 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-26 16:44:51 - NodeExecutor - DEBUG - Set correlation ID to: 496d6e81-a514-452d-8bf5-fb629b599353
2025-06-26 16:44:51 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 496d6e81-a514-452d-8bf5-fb629b599353 in node_executor
2025-06-26 16:44:51 - AgentExecutor - DEBUG - Set correlation ID to: 496d6e81-a514-452d-8bf5-fb629b599353
2025-06-26 16:44:51 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 496d6e81-a514-452d-8bf5-fb629b599353 in agent_executor
2025-06-26 16:44:51 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-26 16:44:51 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-26 16:44:51 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-26 16:44:51 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 496d6e81-a514-452d-8bf5-fb629b599353
2025-06-26 16:44:51 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 496d6e81-a514-452d-8bf5-fb629b599353
2025-06-26 16:44:51 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-26 16:44:51 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-26 16:44:51 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-26 16:44:51 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-26 16:44:51 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-26 16:44:51 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:496d6e81-a514-452d-8bf5-fb629b599353'
2025-06-26 16:44:52 - RedisManager - DEBUG - Set key 'workflow_state:496d6e81-a514-452d-8bf5-fb629b599353' with TTL of 600 seconds
2025-06-26 16:44:52 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 496d6e81-a514-452d-8bf5-fb629b599353. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:44:52 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-26 16:44:52 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-26 16:44:52 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-26 16:44:52 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-26 16:44:52 - StateManager - INFO - Terminated: False
2025-06-26 16:44:52 - StateManager - INFO - Pending transitions (0): []
2025-06-26 16:44:52 - StateManager - INFO - Waiting transitions (0): []
2025-06-26 16:44:52 - StateManager - INFO - Completed transitions (0): []
2025-06-26 16:44:52 - StateManager - INFO - Results stored for 0 transitions
2025-06-26 16:44:52 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 16:44:52 - StateManager - INFO - Workflow status: inactive
2025-06-26 16:44:52 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 16:44:52 - StateManager - INFO - Workflow status: inactive
2025-06-26 16:44:52 - StateManager - INFO - Workflow paused: False
2025-06-26 16:44:52 - StateManager - INFO - ==============================
2025-06-26 16:44:52 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-26 16:44:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 496d6e81-a514-452d-8bf5-fb629b599353):
2025-06-26 16:44:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-26 16:44:52 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-26 16:44:52 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-26 16:44:52 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["bc","de","of"]', 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-26 16:44:52 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["bc","de","of"]', 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-26 16:44:52 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 496d6e81-a514-452d-8bf5-fb629b599353):
2025-06-26 16:44:52 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-26 16:44:52 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1750925348097 (has final/aggregated indicators)
2025-06-26 16:44:52 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-26 16:44:52 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-26 16:44:52 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: bc
2025-06-26 16:44:52 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-26 16:44:53 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-26 16:44:53 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:44:53 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-26 16:44:53 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-26 16:44:53 - StateManager - DEBUG - Stored result for transition current_iteration in memory: bc
2025-06-26 16:44:53 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 16:44:53 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 16:44:53 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:44:53 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 16:44:53 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration'}
2025-06-26 16:44:53 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'bc', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 178502.083682916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:44:54 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 16:44:54 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 16:44:54 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:44:54 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:44:54 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************'}
2025-06-26 16:44:54 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 16:44:54 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 496d6e81-a514-452d-8bf5-fb629b599353):
2025-06-26 16:44:54 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-26 16:44:54 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 16:44:54 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 16:44:54 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 16:44:54 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 16:44:54 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 16:44:55 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 16:44:55 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 16:44:55 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'bc', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 178502.083682916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:44:55 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 16:44:55 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'bc', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 178502.083682916, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': bc
2025-06-26 16:44:55 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 16:44:55 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 16:44:55 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 16:44:55 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 16:44:55 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 16:44:55 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 16:44:55 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:44:55 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:44:55 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:44:55 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 496d6e81-a514-452d-8bf5-fb629b599353):
2025-06-26 16:44:55 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-26 16:44:55 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: de2367c5-9847-4f23-868d-4f865cf79c63) using provided producer.
2025-06-26 16:44:55 - NodeExecutor - DEBUG - Added correlation_id 496d6e81-a514-452d-8bf5-fb629b599353 to payload
2025-06-26 16:44:55 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': 'de2367c5-9847-4f23-868d-4f865cf79c63', 'correlation_id': '496d6e81-a514-452d-8bf5-fb629b599353'}
2025-06-26 16:44:55 - NodeExecutor - DEBUG - Request de2367c5-9847-4f23-868d-4f865cf79c63 sent successfully using provided producer.
2025-06-26 16:44:55 - NodeExecutor - DEBUG - Waiting indefinitely for result for request de2367c5-9847-4f23-868d-4f865cf79c63...
2025-06-26 16:44:55 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 950, corr_id: 496d6e81-a514-452d-8bf5-fb629b599353
2025-06-26 16:45:26 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:45:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:45:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:45:27 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:45:38 - NodeExecutor - DEBUG - Result consumer received message: Offset=722
2025-06-26 16:45:38 - NodeExecutor - DEBUG - Received valid result for request_id de2367c5-9847-4f23-868d-4f865cf79c63
2025-06-26 16:45:38 - NodeExecutor - INFO - Result received for request de2367c5-9847-4f23-868d-4f865cf79c63.
2025-06-26 16:45:38 - TransitionHandler - INFO - Execution result from Components executor: "bc\nhello"
2025-06-26 16:45:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 496d6e81-a514-452d-8bf5-fb629b599353):
2025-06-26 16:45:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'bc\nhello', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 16:45:38 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'bc\nhello'}, 'status': 'completed', 'timestamp': 1750936538.3971741}}
2025-06-26 16:45:39 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 16:45:39 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 16:45:39 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:45:39 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:45:39 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0', 'current_iteration', 'transition-LoopNode-*************', 'transition-CombineTextComponent-*************'}
2025-06-26 16:45:39 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 16:45:39 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 16:45:39 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 44.76 seconds
2025-06-26 16:45:39 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 496d6e81-a514-452d-8bf5-fb629b599353):
2025-06-26 16:45:39 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'result': 'Completed transition in 44.76 seconds', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-26 16:46:26 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:46:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:46:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:46:27 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:47:26 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:47:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:47:27 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:47:27 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
