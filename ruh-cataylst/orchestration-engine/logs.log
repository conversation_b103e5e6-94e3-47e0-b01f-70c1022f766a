2025-06-26 16:49:40 - Main - INFO - Starting Server
2025-06-26 16:49:40 - Main - INFO - Connection at: **************:9092
2025-06-26 16:49:40 - MCPToolExecutor - INFO - KafkaToolExecutor initialized.
2025-06-26 16:49:40 - Node<PERSON>xecutor - INFO - NodeExecutor initialized.
2025-06-26 16:49:40 - AgentExecutor - INFO - AgentExecutor initialized.
2025-06-26 16:49:40 - KafkaWorkflowConsumer - INFO - Initializing database connections...
2025-06-26 16:49:40 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-26 16:49:42 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-26 16:49:42 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-26 16:49:43 - <PERSON><PERSON><PERSON>anager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-26 16:49:45 - PostgresManager - INFO - PostgreSQL connection pool created
2025-06-26 16:49:45 - PostgresManager - INFO - PostgreSQL connection pool is available
2025-06-26 16:49:47 - PostgresManager - INFO - PostgreSQL tables and indexes created or verified.
2025-06-26 16:49:48 - RedisEventListener - INFO - Creating new RedisEventListener instance
2025-06-26 16:49:48 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=5
2025-06-26 16:49:50 - RedisManager - INFO - Successfully connected to Redis on DB index: 5!
2025-06-26 16:49:50 - RedisManager - INFO - Connecting to Redis: host=**************, port=6379, password=provided, db_index=6
2025-06-26 16:49:51 - RedisManager - INFO - Successfully connected to Redis on DB index: 6!
2025-06-26 16:49:51 - RedisEventListener - INFO - Starting Redis event listener thread
2025-06-26 16:49:51 - RedisEventListener - INFO - Redis event listener started
2025-06-26 16:49:51 - KafkaWorkflowConsumer - INFO - Database connections initialized successfully
2025-06-26 16:49:51 - StateManager - DEBUG - Using provided database connections
2025-06-26 16:49:51 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 16:49:51 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 16:49:51 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 16:49:52 - RedisEventListener - INFO - Configured Redis results DB for keyspace notifications including expirations
2025-06-26 16:49:52 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 16:49:52 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 16:49:52 - KafkaWorkflowConsumer - INFO - WorkflowStateManager reference set in RedisEventListener for archival operations
2025-06-26 16:49:52 - KafkaWorkflowConsumer - INFO - KafkaWorkflowConsumer initialized successfully
2025-06-26 16:49:52 - RedisEventListener - INFO - Configured Redis state DB for keyspace notifications including expirations
2025-06-26 16:49:52 - RedisEventListener - INFO - Created dedicated Redis clients for pubsub with decode_responses=False
2025-06-26 16:49:55 - RedisEventListener - INFO - Redis results client decode_responses: True
2025-06-26 16:49:55 - RedisEventListener - INFO - Redis state client decode_responses: True
2025-06-26 16:49:55 - RedisEventListener - INFO - Subscribed to keyspace events for Redis DB 5 and 6
2025-06-26 16:49:55 - RedisEventListener - DEBUG - Raw results DB event message: {'type': 'pmessage', 'pattern': b'__keyspace@5__:*', 'channel': b'__keyspace@5__:result:transition-LoopNode-*************', 'data': b'expired'}
2025-06-26 16:49:55 - RedisEventListener - DEBUG - Channel type: <class 'bytes'>, value: b'__keyspace@5__:result:transition-LoopNode-*************'
2025-06-26 16:49:55 - RedisEventListener - DEBUG - Decoded channel: __keyspace@5__:result:transition-LoopNode-*************
2025-06-26 16:49:55 - RedisEventListener - DEBUG - Extracted key: result:transition-LoopNode-*************
2025-06-26 16:49:55 - RedisEventListener - DEBUG - Event data type: <class 'bytes'>, value: b'expired'
2025-06-26 16:49:55 - RedisEventListener - DEBUG - Decoded event: expired
2025-06-26 16:49:55 - RedisEventListener - INFO - Detected expired event for result of transition: transition-LoopNode-*************
2025-06-26 16:49:55 - RedisEventListener - INFO - Archiving result for transition: transition-LoopNode-*************
2025-06-26 16:49:55 - StateManager - DEBUG - Attempting to archive result for transition transition-LoopNode-*************
2025-06-26 16:49:56 - StateManager - DEBUG - Provided result: False
2025-06-26 16:49:56 - StateManager - DEBUG - Trying to get result from Redis for transition transition-LoopNode-*************
2025-06-26 16:49:56 - StateManager - DEBUG - No result found in Redis for transition transition-LoopNode-*************
2025-06-26 16:49:56 - StateManager - DEBUG - Trying to get result from memory for transition transition-LoopNode-*************
2025-06-26 16:49:56 - StateManager - DEBUG - No result found in memory for transition transition-LoopNode-*************
2025-06-26 16:49:56 - StateManager - DEBUG - Available transition results in memory: []
2025-06-26 16:49:56 - StateManager - WARNING - No result found to archive for transition transition-LoopNode-*************
2025-06-26 16:50:01 - MCPToolExecutor - INFO - Starting KafkaToolExecutor internal consumer...
2025-06-26 16:50:07 - MCPToolExecutor - INFO - Internal consumer started. Listening for results on: 'mcp_results', Group: 'tool-executor-consumer'
2025-06-26 16:50:07 - MCPToolExecutor - INFO - Background result consumer loop started.
2025-06-26 16:50:07 - NodeExecutor - INFO - Starting NodeExecutor internal consumer...
2025-06-26 16:50:13 - NodeExecutor - INFO - Internal consumer started. Listening for results on: 'node_results', Group: 'node-executor-consumer'
2025-06-26 16:50:13 - NodeExecutor - INFO - Background result consumer loop started.
2025-06-26 16:50:13 - AgentExecutor - INFO - Starting AgentExecutor internal consumer...
2025-06-26 16:50:20 - AgentExecutor - INFO - Internal consumer started. Listening for results on: 'agent_chat_responses', Group: 'agent-executor-consumer'
2025-06-26 16:50:20 - AgentExecutor - INFO - Background result consumer loop started.
2025-06-26 16:50:20 - KafkaWorkflowConsumer - INFO - Received: topic=approval-requests, partition=0, offset=213
2025-06-26 16:50:20 - KafkaWorkflowConsumer - ERROR - No running workflow found for correlationId: 496d6e81-a514-452d-8bf5-fb629b599353 (approval-request)
2025-06-26 16:50:20 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 496d6e81-a514-452d-8bf5-fb629b599353, response: {'status': 'error', 'workflow_status': 'failed', 'result': 'No workflow found for approval request', 'sequence': 0}
2025-06-26 16:50:20 - KafkaWorkflowConsumer - INFO - Received: topic=workflow-requests, partition=0, offset=951
2025-06-26 16:50:20 - KafkaWorkflowConsumer - DEBUG - message json: {'task_id': 1750936799, 'task_type': 'workflow', 'data': {'workflow_id': 'b3b7f168-29ee-4e51-8717-16f4c8beb832', 'payload': {'user_dependent_fields': ['iteration_list'], 'user_payload_template': {'iteration_list': {'value': '["bc","de","of"]', 'transition_id': 'LoopNode-*************'}}}, 'approval': True, 'user_id': '91a237fd-0225-4e02-9e9f-805eff073b07'}, 'approval': True}
2025-06-26 16:50:20 - KafkaWorkflowConsumer - INFO - Extracted user_id: 91a237fd-0225-4e02-9e9f-805eff073b07 for workflow: b3b7f168-29ee-4e51-8717-16f4c8beb832
2025-06-26 16:50:20 - WorkflowService - DEBUG - Sending GET request to: https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/b3b7f168-29ee-4e51-8717-16f4c8beb832
2025-06-26 16:50:23 - WorkflowService - DEBUG - Received response with status code: 200
2025-06-26 16:50:23 - WorkflowService - DEBUG - Parsed JSON response: {
  "success": true,
  "message": "Workflow Untitled Workflow retrieved successfully",
  "workflow": {
    "id": "b3b7f168-29ee-4e51-8717-16f4c8beb832",
    "name": "Untitled Workflow",
    "description": "Untitled_Workflow",
    "workflow_url": "https://storage.googleapis.com/ruh-dev/workflows/1f26d850-71b2-48e5-8e47-ddbed745342f.json",
    "builder_url": "https://storage.googleapis.com/ruh-dev/workflow_builders/a08b2b33-545a-4b20-950a-a65a45067d82.json",
    "start_nodes": [
      {
        "field": "iteration_list",
        "type": "list",
        "transition_id": "transition-LoopNode-*************"
      }
    ],
    "owner_id": "91a237fd-0225-4e02-9e9f-805eff073b07",
    "user_ids": [
      "91a237fd-0225-4e02-9e9f-805eff073b07"
    ],
    "owner_type": "user",
    "workflow_template_id": null,
    "template_owner_id": null,
    "is_imported": false,
    "version": "1.0.0",
    "visibility": "private",
    "category": null,
    "tags": null,
    "status": "active",
    "is_changes_marketplace": false,
    "is_customizable": true,
    "auto_version_on_update": false,
    "created_at": "2025-06-26T08:07:41.710172",
    "updated_at": "2025-06-26T11:14:12.174759",
    "available_nodes": [
      {
        "name": "CombineTextComponent",
        "display_name": "Combine Text",
        "type": "component",
        "transition_id": "transition-CombineTextComponent-*************"
      },
      {
        "name": "MergeDataComponent",
        "display_name": "Merge Data",
        "type": "component",
        "transition_id": "transition-MergeDataComponent-1750925348097"
      }
    ],
    "is_updated": true
  }
}
2025-06-26 16:50:27 - KafkaWorkflowConsumer - DEBUG - Workflow loaded for b3b7f168-29ee-4e51-8717-16f4c8beb832 - server_script_path is optional
2025-06-26 16:50:27 - WorkflowUtils - INFO - WorkflowUtils initialized
2025-06-26 16:50:27 - StateManager - DEBUG - Using global database connections from initializer
2025-06-26 16:50:27 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 16:50:27 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 16:50:27 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 16:50:28 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 16:50:28 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 16:50:28 - WorkflowUtils - INFO - Workflow JSON is valid against the enhanced schema.
2025-06-26 16:50:28 - StateManager - DEBUG - Using provided database connections
2025-06-26 16:50:28 - RedisEventListener - INFO - Workflow state manager reference updated
2025-06-26 16:50:28 - StateManager - DEBUG - Set workflow state manager reference in RedisEventListener
2025-06-26 16:50:28 - StateManager - DEBUG - Updated workflow state manager reference in Redis event listener
2025-06-26 16:50:29 - StateManager - INFO - PostgreSQL connection available for persistent storage
2025-06-26 16:50:29 - StateManager - INFO - WorkflowStateManager initialized
2025-06-26 16:50:29 - StateManager - DEBUG - Extracted dependencies for transition transition-MergeDataComponent-1750925348097: ['transition-LoopNode-*************']
2025-06-26 16:50:29 - StateManager - DEBUG - Extracted dependencies for transition transition-CombineTextComponent-*************: ['transition-LoopNode-*************']
2025-06-26 16:50:29 - StateManager - INFO - Built dependency map for 3 transitions
2025-06-26 16:50:29 - StateManager - DEBUG - Transition transition-MergeDataComponent-1750925348097 depends on: ['transition-LoopNode-*************']
2025-06-26 16:50:29 - StateManager - DEBUG - Transition transition-CombineTextComponent-************* depends on: ['transition-LoopNode-*************']
2025-06-26 16:50:29 - MCPToolExecutor - DEBUG - Set correlation ID to: 4e28a473-fdf4-4bd6-988c-f666c08a7466
2025-06-26 16:50:29 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 in tool_executor
2025-06-26 16:50:29 - MCPToolExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-26 16:50:29 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in tool_executor
2025-06-26 16:50:29 - NodeExecutor - DEBUG - Set correlation ID to: 4e28a473-fdf4-4bd6-988c-f666c08a7466
2025-06-26 16:50:29 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 in node_executor
2025-06-26 16:50:29 - AgentExecutor - DEBUG - Set correlation ID to: 4e28a473-fdf4-4bd6-988c-f666c08a7466
2025-06-26 16:50:29 - EnhancedWorkflowEngine - DEBUG - Set correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 in agent_executor
2025-06-26 16:50:29 - AgentExecutor - DEBUG - Set user ID to: 91a237fd-0225-4e02-9e9f-805eff073b07
2025-06-26 16:50:29 - EnhancedWorkflowEngine - DEBUG - Set user_id 91a237fd-0225-4e02-9e9f-805eff073b07 in agent_executor
2025-06-26 16:50:29 - TransitionHandler - INFO - TransitionHandler initialized
2025-06-26 16:50:29 - EnhancedWorkflowEngine - INFO - EnhancedWorkflowEngine initialized with workflow ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466
2025-06-26 16:50:29 - KafkaWorkflowConsumer - INFO - Workflow execution started in background for task-request, corr_id: 4e28a473-fdf4-4bd6-988c-f666c08a7466
2025-06-26 16:50:29 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'status': 'Workflow Initialized', 'result': 'Workflow Initialized', 'workflow_status': 'running'}
2025-06-26 16:50:29 - StateManager - INFO - Workflow initialized with initial transition: transition-LoopNode-*************
2025-06-26 16:50:29 - StateManager - DEBUG - State: pending={'transition-LoopNode-*************'}, waiting=set(), completed=set()
2025-06-26 16:50:29 - EnhancedWorkflowEngine - INFO - Initializing workflow with single initial transition: transition-LoopNode-*************
2025-06-26 16:50:29 - StateManager - DEBUG - Workflow active: {'transition-LoopNode-*************'}
2025-06-26 16:50:29 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:4e28a473-fdf4-4bd6-988c-f666c08a7466'
2025-06-26 16:50:30 - RedisManager - DEBUG - Set key 'workflow_state:4e28a473-fdf4-4bd6-988c-f666c08a7466' with TTL of 600 seconds
2025-06-26 16:50:30 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:30 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-26 16:50:30 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-26 16:50:30 - StateManager - INFO - Cleared 1 pending transitions: {'transition-LoopNode-*************'}
2025-06-26 16:50:30 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-26 16:50:30 - StateManager - INFO - Terminated: False
2025-06-26 16:50:30 - StateManager - INFO - Pending transitions (0): []
2025-06-26 16:50:30 - StateManager - INFO - Waiting transitions (0): []
2025-06-26 16:50:30 - StateManager - INFO - Completed transitions (0): []
2025-06-26 16:50:30 - StateManager - INFO - Results stored for 0 transitions
2025-06-26 16:50:30 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 16:50:30 - StateManager - INFO - Workflow status: inactive
2025-06-26 16:50:30 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 16:50:30 - StateManager - INFO - Workflow status: inactive
2025-06-26 16:50:30 - StateManager - INFO - Workflow paused: False
2025-06-26 16:50:30 - StateManager - INFO - ==============================
2025-06-26 16:50:30 - TransitionHandler - INFO - Starting parallel execution of transition: transition-LoopNode-*************
2025-06-26 16:50:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 0, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Starting execution of transition: transition-LoopNode-*************', 'status': 'started', 'sequence': 0, 'workflow_status': 'running'}
2025-06-26 16:50:30 - TransitionHandler - EXECUTE - Transition 'transition-LoopNode-*************' (type=initial, execution_type=loop)
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 🔗 Set orchestration engine for loop executor
2025-06-26 16:50:30 - TransitionHandler - INFO - Using KafkaToolExecutor for execution_type: loop
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-LoopNode-*************
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: loop
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for loop node
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 📝 No previous results found, using static parameters
2025-06-26 16:50:30 - TransitionHandler - DEBUG - tool Parameters: {'source_type': 'iteration_list', 'iteration_list': '["bc","de","of"]', 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-26 16:50:30 - TransitionHandler - INFO - Invoking tool 'LoopNode' (tool_id: 1) for node 'LoopNode' in transition 'transition-LoopNode-*************' with parameters: {'source_type': 'iteration_list', 'iteration_list': '["bc","de","of"]', 'batch_size': '1', 'start': '1', 'end': '10', 'step': '1', 'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60, 'aggregation_type': 'collect_all', 'include_metadata': True, 'on_iteration_error': 'continue', 'include_errors': True}
2025-06-26 16:50:30 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 1, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:30 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'result': 'Connecting to server LoopNode', 'status': 'connecting', 'sequence': 1, 'workflow_status': 'running'}
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 🔄 Resolving loop config parameters for transition: transition-LoopNode-*************
2025-06-26 16:50:30 - TransitionHandler - DEBUG - ✅ Loop config parameter resolution completed for transition: transition-LoopNode-*************
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1750925348097 (has final/aggregated indicators)
2025-06-26 16:50:30 - TransitionHandler - INFO - 🔍 Auto-detected and added loop body transitions to config: ['transition-CombineTextComponent-*************']
2025-06-26 16:50:30 - TransitionHandler - DEBUG - 📝 Registered loop executor for transition: transition-LoopNode-*************
2025-06-26 16:50:30 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in memory: bc
2025-06-26 16:50:30 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_0
2025-06-26 16:50:31 - RedisManager - DEBUG - Set key 'result:loop_iteration_0' with TTL of 900 seconds
2025-06-26 16:50:31 - StateManager - DEBUG - Stored result for transition loop_iteration_0 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:31 - StateManager - INFO - Marked transition loop_iteration_0 as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:31 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_0'}
2025-06-26 16:50:31 - StateManager - DEBUG - Stored result for transition current_iteration in memory: bc
2025-06-26 16:50:31 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 16:50:31 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 16:50:31 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:31 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:31 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0'}
2025-06-26 16:50:31 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'bc', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 178840.070983541, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:32 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 16:50:32 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 16:50:32 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:32 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:32 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 16:50:32 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 16:50:32 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 2, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:32 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 2, 'workflow_status': 'running'}
2025-06-26 16:50:32 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 16:50:32 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 16:50:32 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 16:50:32 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 16:50:32 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 16:50:33 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 16:50:33 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 16:50:33 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'bc', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 178840.070983541, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:50:33 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 16:50:33 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'bc', 'iteration_index': 0, 'iteration_metadata': {'timestamp': 178840.070983541, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': bc
2025-06-26 16:50:33 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 16:50:33 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 16:50:33 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 16:50:33 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 16:50:33 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 16:50:33 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 16:50:33 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:33 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:33 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:33 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 3, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:33 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 3, 'workflow_status': 'running'}
2025-06-26 16:50:33 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 9962fd8f-5dfc-4294-82fa-28448ccb14a5) using provided producer.
2025-06-26 16:50:33 - NodeExecutor - DEBUG - Added correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 to payload
2025-06-26 16:50:33 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'bc', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': '9962fd8f-5dfc-4294-82fa-28448ccb14a5', 'correlation_id': '4e28a473-fdf4-4bd6-988c-f666c08a7466'}
2025-06-26 16:50:33 - NodeExecutor - DEBUG - Request 9962fd8f-5dfc-4294-82fa-28448ccb14a5 sent successfully using provided producer.
2025-06-26 16:50:33 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 9962fd8f-5dfc-4294-82fa-28448ccb14a5...
2025-06-26 16:50:33 - KafkaWorkflowConsumer - INFO - Committed offset after starting engine for task-request: 951, corr_id: 4e28a473-fdf4-4bd6-988c-f666c08a7466
2025-06-26 16:50:38 - NodeExecutor - DEBUG - Result consumer received message: Offset=723
2025-06-26 16:50:38 - NodeExecutor - DEBUG - Received valid result for request_id 9962fd8f-5dfc-4294-82fa-28448ccb14a5
2025-06-26 16:50:38 - NodeExecutor - INFO - Result received for request 9962fd8f-5dfc-4294-82fa-28448ccb14a5.
2025-06-26 16:50:38 - TransitionHandler - INFO - Execution result from Components executor: "bc\nhello"
2025-06-26 16:50:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 4, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'bc\nhello', 'status': 'completed', 'sequence': 4, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 16:50:38 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'bc\nhello'}, 'status': 'completed', 'timestamp': 1750936838.201949}}
2025-06-26 16:50:38 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 16:50:38 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 16:50:38 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:38 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:38 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'current_iteration', 'transition-CombineTextComponent-*************', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 16:50:38 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 16:50:38 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 16:50:38 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 6.31 seconds
2025-06-26 16:50:38 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 5, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:38 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Completed transition in 6.31 seconds', 'status': 'time_logged', 'sequence': 5, 'workflow_status': 'running'}
2025-06-26 16:50:38 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in memory: de
2025-06-26 16:50:39 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_1
2025-06-26 16:50:39 - RedisManager - DEBUG - Set key 'result:loop_iteration_1' with TTL of 900 seconds
2025-06-26 16:50:39 - StateManager - DEBUG - Stored result for transition loop_iteration_1 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:39 - StateManager - INFO - Marked transition loop_iteration_1 as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:39 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 16:50:39 - StateManager - DEBUG - Stored result for transition current_iteration in memory: de
2025-06-26 16:50:39 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 16:50:40 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 16:50:40 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:40 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:40 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 16:50:40 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'de', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 178848.685063375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:41 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 16:50:41 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 16:50:41 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:41 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:41 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 16:50:41 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 16:50:41 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 6, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:41 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 6, 'workflow_status': 'running'}
2025-06-26 16:50:41 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 16:50:41 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 16:50:41 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 16:50:41 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 16:50:41 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 16:50:42 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 16:50:42 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 16:50:42 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'de', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 178848.685063375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:50:42 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 16:50:42 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'de', 'iteration_index': 1, 'iteration_metadata': {'timestamp': 178848.685063375, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': de
2025-06-26 16:50:42 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 16:50:42 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 16:50:42 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 16:50:42 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 16:50:42 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 16:50:42 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 16:50:42 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'de', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:42 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'de', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:42 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'de', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 7, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 7, 'workflow_status': 'running'}
2025-06-26 16:50:42 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 8a4564d8-3226-482f-8fca-346b2723f778) using provided producer.
2025-06-26 16:50:42 - NodeExecutor - DEBUG - Added correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 to payload
2025-06-26 16:50:42 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'de', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': '8a4564d8-3226-482f-8fca-346b2723f778', 'correlation_id': '4e28a473-fdf4-4bd6-988c-f666c08a7466'}
2025-06-26 16:50:42 - NodeExecutor - DEBUG - Request 8a4564d8-3226-482f-8fca-346b2723f778 sent successfully using provided producer.
2025-06-26 16:50:42 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 8a4564d8-3226-482f-8fca-346b2723f778...
2025-06-26 16:50:42 - NodeExecutor - DEBUG - Result consumer received message: Offset=724
2025-06-26 16:50:42 - NodeExecutor - DEBUG - Received valid result for request_id 8a4564d8-3226-482f-8fca-346b2723f778
2025-06-26 16:50:42 - NodeExecutor - INFO - Result received for request 8a4564d8-3226-482f-8fca-346b2723f778.
2025-06-26 16:50:42 - TransitionHandler - INFO - Execution result from Components executor: "de\nhello"
2025-06-26 16:50:42 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 8, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:42 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'de\nhello', 'status': 'completed', 'sequence': 8, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 16:50:42 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'de\nhello'}, 'status': 'completed', 'timestamp': 1750936842.590436}}
2025-06-26 16:50:43 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 16:50:43 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 16:50:43 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:43 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:43 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************'}
2025-06-26 16:50:43 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 16:50:43 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 16:50:43 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.09 seconds
2025-06-26 16:50:43 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 9, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:43 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Completed transition in 2.09 seconds', 'status': 'time_logged', 'sequence': 9, 'workflow_status': 'running'}
2025-06-26 16:50:43 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in memory: of
2025-06-26 16:50:43 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: loop_iteration_2
2025-06-26 16:50:44 - RedisManager - DEBUG - Set key 'result:loop_iteration_2' with TTL of 900 seconds
2025-06-26 16:50:44 - StateManager - DEBUG - Stored result for transition loop_iteration_2 in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:44 - StateManager - INFO - Marked transition loop_iteration_2 as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:44 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************', 'loop_iteration_2'}
2025-06-26 16:50:44 - StateManager - DEBUG - Stored result for transition current_iteration in memory: of
2025-06-26 16:50:44 - StateManager - DEBUG - Using extended TTL 900s for loop iteration result: current_iteration
2025-06-26 16:50:44 - RedisManager - DEBUG - Set key 'result:current_iteration' with TTL of 900 seconds
2025-06-26 16:50:44 - StateManager - DEBUG - Stored result for transition current_iteration in Redis with TTL 900s. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:44 - StateManager - INFO - Marked transition current_iteration as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:44 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************', 'loop_iteration_2'}
2025-06-26 16:50:44 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: {'current_item': 'of', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 178853.076042625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:45 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 16:50:45 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 16:50:45 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:45 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:45 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************', 'loop_iteration_2'}
2025-06-26 16:50:45 - TransitionHandler - INFO - Starting parallel execution of transition: transition-CombineTextComponent-*************
2025-06-26 16:50:45 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 10, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:45 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Starting execution of transition: transition-CombineTextComponent-*************', 'status': 'started', 'sequence': 10, 'workflow_status': 'running'}
2025-06-26 16:50:45 - TransitionHandler - EXECUTE - Transition 'transition-CombineTextComponent-*************' (type=standard, execution_type=Components)
2025-06-26 16:50:45 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 16:50:45 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-CombineTextComponent-*************
2025-06-26 16:50:45 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 16:50:45 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 16:50:46 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 16:50:46 - StateManager - DEBUG - Extracted results for 3 tools in transition transition-LoopNode-*************
2025-06-26 16:50:46 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'of', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 178853.076042625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:50:46 - WorkflowUtils - INFO - 🔍 Handle mapping validation: fully_compatible (1/1 compatible)
2025-06-26 16:50:46 - TransitionHandler - INFO - 🔍 Handle validation: fully_compatible (1/1 compatible)
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Processing source_results for handle 'current_item': {'current_item': 'of', 'iteration_index': 2, 'iteration_metadata': {'timestamp': 178853.076042625, 'loop_id': 'transition-LoopNode-*************', 'total_iterations': 3}}
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - 🔍 PATH TRACKING: Available keys in source_results: ['current_item', 'iteration_index', 'iteration_metadata']
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Path tracking - Found handle 'current_item' directly in source_results
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - ✅ Handle mapping success: current_item → main_input via path 'current_item': of
2025-06-26 16:50:46 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 1/1 successful
2025-06-26 16:50:46 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 1/1 successful
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with null/empty value: 
2025-06-26 16:50:46 - WorkflowUtils - INFO - 🧹 Parameter filtering: 13 → 3 fields (10 null/empty fields removed)
2025-06-26 16:50:46 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 1
2025-06-26 16:50:46 - TransitionHandler - DEBUG - 📌 Added static parameter: separator = \n
2025-06-26 16:50:46 - TransitionHandler - DEBUG - 📌 Added static parameter: input_1 = hello
2025-06-26 16:50:46 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'main_input': 'of', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:46 - TransitionHandler - DEBUG - tool Parameters: {'main_input': 'of', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:46 - TransitionHandler - INFO - Invoking tool 'CombineTextComponent' (tool_id: 1) for node 'CombineTextComponent' in transition 'transition-CombineTextComponent-*************' with parameters: {'main_input': 'of', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}
2025-06-26 16:50:46 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 11, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:46 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'Connecting to server CombineTextComponent', 'status': 'connecting', 'sequence': 11, 'workflow_status': 'running'}
2025-06-26 16:50:46 - NodeExecutor - INFO - Executing tool 'CombineTextComponent' via Kafka (request_id: 08e73e63-5cc3-45da-8bf1-b6b8534fbc99) using provided producer.
2025-06-26 16:50:46 - NodeExecutor - DEBUG - Added correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 to payload
2025-06-26 16:50:46 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'CombineTextComponent', 'tool_parameters': {'main_input': 'of', 'num_additional_inputs': '1', 'separator': '\\n', 'input_1': 'hello'}, 'request_id': '08e73e63-5cc3-45da-8bf1-b6b8534fbc99', 'correlation_id': '4e28a473-fdf4-4bd6-988c-f666c08a7466'}
2025-06-26 16:50:46 - NodeExecutor - DEBUG - Request 08e73e63-5cc3-45da-8bf1-b6b8534fbc99 sent successfully using provided producer.
2025-06-26 16:50:46 - NodeExecutor - DEBUG - Waiting indefinitely for result for request 08e73e63-5cc3-45da-8bf1-b6b8534fbc99...
2025-06-26 16:50:46 - NodeExecutor - DEBUG - Result consumer received message: Offset=725
2025-06-26 16:50:47 - NodeExecutor - DEBUG - Received valid result for request_id 08e73e63-5cc3-45da-8bf1-b6b8534fbc99
2025-06-26 16:50:47 - NodeExecutor - INFO - Result received for request 08e73e63-5cc3-45da-8bf1-b6b8534fbc99.
2025-06-26 16:50:47 - TransitionHandler - INFO - Execution result from Components executor: "of\nhello"
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 12, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': 'of\nhello', 'status': 'completed', 'sequence': 12, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 16:50:47 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in memory: {'CombineTextComponent': {'transition_id': 'transition-CombineTextComponent-*************', 'node_id': 'CombineTextComponent', 'tool_name': 'CombineTextComponent', 'result': {'result': 'of\nhello'}, 'status': 'completed', 'timestamp': 1750936847.001133}}
2025-06-26 16:50:47 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-CombineTextComponent-*************'
2025-06-26 16:50:47 - RedisManager - DEBUG - Set key 'result:transition-CombineTextComponent-*************' with TTL of 300 seconds
2025-06-26 16:50:47 - StateManager - DEBUG - Stored result for transition transition-CombineTextComponent-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:47 - StateManager - INFO - Marked transition transition-CombineTextComponent-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:47 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************', 'loop_iteration_2'}
2025-06-26 16:50:47 - TransitionHandler - DEBUG - ✅ Notified loop executor transition-LoopNode-************* about transition transition-CombineTextComponent-*************
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀 _handle_transition_routing called for transition-CombineTextComponent-*************
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀 Execution result type: <class 'str'>
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀 Execution result keys: not dict
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔍 is_conditional_component_result: Not a dict, type=<class 'str'>
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀 Component routing check for transition-CombineTextComponent-*************:
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀   - is_conditional_result: False
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀   - is_conditional_transition: False
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🔀 No routing logic found for transition-CombineTextComponent-*************, returning empty list
2025-06-26 16:50:47 - TransitionHandler - INFO - Completed transition transition-CombineTextComponent-************* in 2.10 seconds
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 13, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Completed transition in 2.10 seconds', 'status': 'time_logged', 'sequence': 13, 'workflow_status': 'running'}
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 14, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'status': 'loop_completed', 'metadata': {'success': True, 'final_results': ['bc\nhello', 'de\nhello', 'of\nhello'], 'iteration_count': 3, 'total_iterations': 3}, 'transition_id': 'transition-LoopNode-*************', 'sequence': 14, 'workflow_status': 'running'}
2025-06-26 16:50:47 - TransitionHandler - DEBUG - 🗑️ Unregistered loop executor for transition: transition-LoopNode-*************
2025-06-26 16:50:47 - TransitionHandler - INFO - Execution result from loop executor: [
  "bc\nhello",
  "de\nhello",
  "of\nhello"
]
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 15, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:47 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-LoopNode-*************', 'node_id': 'LoopNode', 'tool_name': 'LoopNode', 'result': ['bc\nhello', 'de\nhello', 'of\nhello'], 'status': 'completed', 'sequence': 15, 'workflow_status': 'running', 'approval_required': False}
2025-06-26 16:50:47 - TransitionHandler - INFO - 🔄 Handling loop completion for transition: transition-LoopNode-*************
2025-06-26 16:50:47 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in memory: ['bc\nhello', 'de\nhello', 'of\nhello']
2025-06-26 16:50:48 - RedisManager - DEBUG - Using default results TTL: 300 seconds for key 'result:transition-LoopNode-*************'
2025-06-26 16:50:48 - RedisManager - DEBUG - Set key 'result:transition-LoopNode-*************' with TTL of 300 seconds
2025-06-26 16:50:48 - StateManager - DEBUG - Stored result for transition transition-LoopNode-************* in Redis with TTL Nones. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:48 - StateManager - INFO - Marked transition transition-LoopNode-************* as completed (was_pending=False, was_waiting=False)
2025-06-26 16:50:48 - StateManager - DEBUG - Updated state: pending=set(), waiting=set(), completed={'loop_iteration_1', 'transition-CombineTextComponent-*************', 'current_iteration', 'loop_iteration_0', 'transition-LoopNode-*************', 'loop_iteration_2'}
2025-06-26 16:50:48 - TransitionHandler - DEBUG - 🔍 Auto-detected loop body transition: transition-CombineTextComponent-************* (has current_item/iteration indicators)
2025-06-26 16:50:48 - TransitionHandler - DEBUG - 🔍 Detected exit transition: transition-MergeDataComponent-1750925348097 (has final/aggregated indicators)
2025-06-26 16:50:48 - TransitionHandler - INFO - 🔍 Auto-detected loop body transitions: ['transition-CombineTextComponent-*************']
2025-06-26 16:50:48 - TransitionHandler - DEBUG - 🔍 Loop completion filtering - Loop config: {'iteration_behavior': 'independent', 'iteration_source': {'iteration_list': ['bc', 'de', 'of'], 'batch_size': 1}, 'exit_condition': {'condition_type': 'all_items_processed'}, 'iteration_settings': {'parallel_execution': True, 'max_concurrent': 3, 'preserve_order': True, 'iteration_timeout': 60}, 'result_aggregation': {'aggregation_type': 'collect_all', 'include_metadata': True}, 'loop_body_configuration': {'entry_transitions': ['transition-CombineTextComponent-*************'], 'exit_transitions': ['transition-CombineTextComponent-*************'], 'chain_completion_detection': 'explicit_exit_transitions', 'chain_execution_timeout': 300, 'chain_monitoring': {'enable_progress_tracking': True, 'transition_completion_callbacks': True, 'state_persistence': False}, 'auto_detection_config': {'enable_auto_detection': True, 'detection_strategy': 'hybrid'}}, 'error_handling': {'on_iteration_error': 'continue', 'include_errors': True}}
2025-06-26 16:50:48 - TransitionHandler - DEBUG - 🔍 Loop body transitions to filter: ['transition-CombineTextComponent-*************']
2025-06-26 16:50:48 - TransitionHandler - DEBUG - 🔍 Output data configs: [{'to_transition_id': 'transition-CombineTextComponent-*************', 'target_node_id': 'Combine Text', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'current_item', 'result_path': 'current_item', 'edge_id': 'reactflow__edge-LoopNode-*************current_item-CombineTextComponent-*************main_input'}]}}, {'to_transition_id': 'transition-MergeDataComponent-1750925348097', 'target_node_id': 'Merge Data', 'data_type': 'string', 'output_handle_registry': {'handle_mappings': [{'handle_id': 'final_results', 'result_path': 'final_results', 'edge_id': 'reactflow__edge-LoopNode-*************final_results-MergeDataComponent-1750925348097main_input'}]}}]
2025-06-26 16:50:48 - TransitionHandler - DEBUG - 🚫 Skipping loop body transition 'transition-CombineTextComponent-*************' - should have been executed internally by loop executor
2025-06-26 16:50:48 - TransitionHandler - DEBUG - ✅ Adding exit transition 'transition-MergeDataComponent-1750925348097' to next transitions
2025-06-26 16:50:48 - TransitionHandler - INFO - ✅ Loop completion handled for transition-LoopNode-*************. Next transitions: ['transition-MergeDataComponent-1750925348097']
2025-06-26 16:50:48 - TransitionHandler - INFO - 📊 Loop executed 3 iterations successfully
2025-06-26 16:50:48 - TransitionHandler - INFO - Completed transition transition-LoopNode-************* in 18.44 seconds
2025-06-26 16:50:48 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 16, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:48 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Completed transition in 18.44 seconds', 'status': 'time_logged', 'sequence': 16, 'workflow_status': 'running'}
2025-06-26 16:50:48 - EnhancedWorkflowEngine - DEBUG - Results: [['transition-MergeDataComponent-1750925348097']]
2025-06-26 16:50:48 - EnhancedWorkflowEngine - INFO - Transition transition-LoopNode-************* completed successfully: 1 next transitions
2025-06-26 16:50:48 - TransitionHandler - INFO - Resolved next transitions (direct transition IDs): ['transition-MergeDataComponent-1750925348097']
2025-06-26 16:50:48 - EnhancedWorkflowEngine - INFO - Adding transition transition-MergeDataComponent-1750925348097 to pending (all dependencies met)
2025-06-26 16:50:48 - StateManager - DEBUG - Workflow active: {'transition-MergeDataComponent-1750925348097'}
2025-06-26 16:50:48 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:50:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:50:49 - RedisManager - DEBUG - Using default state TTL: 600 seconds for key 'workflow_state:4e28a473-fdf4-4bd6-988c-f666c08a7466'
2025-06-26 16:50:49 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:50:49 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:50:49 - RedisManager - DEBUG - Set key 'workflow_state:4e28a473-fdf4-4bd6-988c-f666c08a7466' with TTL of 600 seconds
2025-06-26 16:50:49 - StateManager - INFO - Workflow state saved to Redis for workflow ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466. Will be archived to PostgreSQL when Redis key expires.
2025-06-26 16:50:49 - StateManager - DEBUG - Checking waiting transitions: set()
2025-06-26 16:50:49 - StateManager - DEBUG - No waiting transitions are ready to move to pending
2025-06-26 16:50:49 - StateManager - INFO - Cleared 1 pending transitions: {'transition-MergeDataComponent-1750925348097'}
2025-06-26 16:50:49 - StateManager - INFO - === WORKFLOW STATE SNAPSHOT ===
2025-06-26 16:50:49 - StateManager - INFO - Terminated: False
2025-06-26 16:50:49 - StateManager - INFO - Pending transitions (0): []
2025-06-26 16:50:49 - StateManager - INFO - Waiting transitions (0): []
2025-06-26 16:50:49 - StateManager - INFO - Completed transitions (6): ['current_iteration', 'loop_iteration_0', 'loop_iteration_1', 'loop_iteration_2', 'transition-CombineTextComponent-*************', 'transition-LoopNode-*************']
2025-06-26 16:50:49 - StateManager - INFO - Results stored for 6 transitions
2025-06-26 16:50:49 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 16:50:49 - StateManager - INFO - Workflow status: inactive
2025-06-26 16:50:49 - StateManager - DEBUG - Workflow active: set() (pending=empty, waiting=empty)
2025-06-26 16:50:49 - StateManager - INFO - Workflow status: inactive
2025-06-26 16:50:49 - StateManager - INFO - Workflow paused: False
2025-06-26 16:50:49 - StateManager - INFO - ==============================
2025-06-26 16:50:49 - TransitionHandler - INFO - Starting parallel execution of transition: transition-MergeDataComponent-1750925348097
2025-06-26 16:50:49 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 17, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:49 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'result': 'Starting execution of transition: transition-MergeDataComponent-1750925348097', 'status': 'started', 'sequence': 17, 'workflow_status': 'running'}
2025-06-26 16:50:49 - TransitionHandler - EXECUTE - Transition 'transition-MergeDataComponent-1750925348097' (type=standard, execution_type=Components)
2025-06-26 16:50:49 - TransitionHandler - INFO - Using NodeExecutor for execution_type: Components
2025-06-26 16:50:49 - TransitionHandler - DEBUG - 🔧 Starting universal parameter resolution for transition: transition-MergeDataComponent-1750925348097
2025-06-26 16:50:49 - TransitionHandler - DEBUG - 📊 Using result_resolution metadata: component
2025-06-26 16:50:49 - TransitionHandler - DEBUG - 🎯 Enhanced parameter resolution for component node
2025-06-26 16:50:50 - StateManager - DEBUG - Retrieved result for transition transition-LoopNode-************* from Redis
2025-06-26 16:50:50 - StateManager - DEBUG - Raw result is not a dict for transition transition-LoopNode-*************, returning as-is
2025-06-26 16:50:50 - TransitionHandler - DEBUG - 📥 Collected results from transition transition-LoopNode-*************
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Added 1 handle mappings from transition transition-LoopNode-************* (total: 1)
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Source results is not a dict for handle final_results: <class 'list'>
2025-06-26 16:50:50 - WorkflowUtils - INFO - 🔍 Handle mapping validation: incompatible (0/1 compatible)
2025-06-26 16:50:50 - TransitionHandler - INFO - 🔍 Handle validation: incompatible (0/1 compatible)
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Source results is not a dict for handle final_results: <class 'list'>
2025-06-26 16:50:50 - WorkflowUtils - WARNING - ❌ Handle mapping failed: final_results → main_input (no data found)
2025-06-26 16:50:50 - WorkflowUtils - INFO - 🎯 Universal parameter mapping complete: 0/1 successful
2025-06-26 16:50:50 - WorkflowUtils - WARNING - ⚠️ 1 universal handle mappings failed - this may cause tool execution errors
2025-06-26 16:50:50 - TransitionHandler - INFO - 🎯 Parameter mapping complete: 0/1 successful
2025-06-26 16:50:50 - TransitionHandler - WARNING - ❌ Failed mapping: final_results → main_input (Error: No data found for handle final_results)
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'main_input' with null/empty value: None
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_1' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_2' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_3' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_4' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_5' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_6' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_7' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_8' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_9' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_10' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'output_key_11' with null/empty value: 
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_1' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_2' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_3' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_4' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_5' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_6' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_7' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_8' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_9' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - DEBUG - Filtering out field 'input_10' with empty collection: {}
2025-06-26 16:50:50 - WorkflowUtils - INFO - 🧹 Parameter filtering: 24 → 2 fields (22 null/empty fields removed)
2025-06-26 16:50:50 - TransitionHandler - DEBUG - 📌 Added static parameter: num_additional_inputs = 2
2025-06-26 16:50:50 - TransitionHandler - DEBUG - 📌 Added static parameter: merge_strategy = Overwrite
2025-06-26 16:50:50 - TransitionHandler - DEBUG - ✅ Final resolved parameters: {'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}
2025-06-26 16:50:50 - TransitionHandler - DEBUG - tool Parameters: {'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}
2025-06-26 16:50:50 - TransitionHandler - INFO - Invoking tool 'MergeDataComponent' (tool_id: 1) for node 'MergeDataComponent' in transition 'transition-MergeDataComponent-1750925348097' with parameters: {'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}
2025-06-26 16:50:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 18, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-MergeDataComponent-1750925348097', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': 'Connecting to server MergeDataComponent', 'status': 'connecting', 'sequence': 18, 'workflow_status': 'running'}
2025-06-26 16:50:50 - NodeExecutor - INFO - Executing tool 'MergeDataComponent' via Kafka (request_id: d501693b-b4ca-44eb-8a6c-cc72a1a71df8) using provided producer.
2025-06-26 16:50:50 - NodeExecutor - DEBUG - Added correlation_id 4e28a473-fdf4-4bd6-988c-f666c08a7466 to payload
2025-06-26 16:50:50 - NodeExecutor - DEBUG - Sending request to topic 'node-execution-request': {'tool_name': 'MergeDataComponent', 'tool_parameters': {'num_additional_inputs': 2, 'merge_strategy': 'Overwrite'}, 'request_id': 'd501693b-b4ca-44eb-8a6c-cc72a1a71df8', 'correlation_id': '4e28a473-fdf4-4bd6-988c-f666c08a7466'}
2025-06-26 16:50:50 - NodeExecutor - DEBUG - Request d501693b-b4ca-44eb-8a6c-cc72a1a71df8 sent successfully using provided producer.
2025-06-26 16:50:50 - NodeExecutor - DEBUG - Waiting indefinitely for result for request d501693b-b4ca-44eb-8a6c-cc72a1a71df8...
2025-06-26 16:50:50 - NodeExecutor - DEBUG - Result consumer received message: Offset=726
2025-06-26 16:50:50 - NodeExecutor - WARNING - Received error response for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - NodeExecutor - ERROR - Error during node execution d501693b-b4ca-44eb-8a6c-cc72a1a71df8: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 339, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - TransitionHandler - ERROR - Tool execution failed for tool 'MergeDataComponent' (tool_id: 1) in node 'MergeDataComponent' of transition 'transition-MergeDataComponent-1750925348097': Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 455, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        tool_name=tool_name, tool_parameters=tool_parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 356, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 339, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

2025-06-26 16:50:50 - KafkaWorkflowConsumer - INFO - Task Result callback (sequence 19, corr_id 4e28a473-fdf4-4bd6-988c-f666c08a7466):
2025-06-26 16:50:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'transition_id': 'transition-MergeDataComponent-1750925348097', 'node_id': 'MergeDataComponent', 'tool_name': 'MergeDataComponent', 'result': '[ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field \'main_input\' not found in parameters"', 'status': 'failed', 'sequence': 19, 'workflow_status': 'running'}
2025-06-26 16:50:50 - TransitionHandler - ERROR - Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - EnhancedWorkflowEngine - DEBUG - Results: [Exception('Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field \'main_input\' not found in parameters"')]
2025-06-26 16:50:50 - EnhancedWorkflowEngine - ERROR - Error in execution of transition transition-MergeDataComponent-1750925348097: Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - EnhancedWorkflowEngine - ERROR - Traceback for transition transition-MergeDataComponent-1750925348097: NoneType: None

2025-06-26 16:50:50 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during parallel execution of standard transitions: Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 455, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        tool_name=tool_name, tool_parameters=tool_parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 356, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 339, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 202, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 659, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 223, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

2025-06-26 16:50:50 - EnhancedWorkflowEngine - ERROR - An unexpected error occurred during workflow execution: Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - EnhancedWorkflowEngine - ERROR - Traceback for unexpected error: Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 455, in _execute_standard_or_reflection_transition
    execution_result = await executor.execute_tool(
                       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        tool_name=tool_name, tool_parameters=tool_parameters
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 356, in execute_tool
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/services/node_executor.py", line 339, in execute_tool
    result = await future
             ^^^^^^^^^^^^
app.services.node_executor.NodeExecutionError: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 202, in _execute_transition_with_tracking
    result = await self._execute_standard_or_reflection_transition(transition)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 659, in _execute_standard_or_reflection_transition
    raise Exception(f"Tool execution error: {error_message}")
Exception: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 280, in execute
    raise e
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/executor_core.py", line 263, in execute
    raise result
  File "/Users/<USER>/ruh/ruh-cataylst/orchestration-engine/app/core_/transition_handler.py", line 223, in _execute_transition_with_tracking
    raise Exception(f"Exception in transition {transition_id}: {str(e)}")
Exception: Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"

2025-06-26 16:50:50 - KafkaWorkflowConsumer - ERROR - Exception in workflow execution: Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - KafkaWorkflowConsumer - INFO - Workflow 'b3b7f168-29ee-4e51-8717-16f4c8beb832' final status: failed, result: Exception in workflow 'b3b7f168-29ee-4e51-8717-16f4c8beb832': Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field 'main_input' not found in parameters"
2025-06-26 16:50:50 - KafkaWorkflowConsumer - INFO - Sent response to workflow-responses with correlation ID: 4e28a473-fdf4-4bd6-988c-f666c08a7466, response: {'status': 'failed', 'result': 'Exception in workflow \'b3b7f168-29ee-4e51-8717-16f4c8beb832\': Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field \'main_input\' not found in parameters"', 'workflow_status': 'failed', 'error': 'Exception in transition transition-MergeDataComponent-1750925348097: Tool execution error: [ERROR] Tool Execution Failed with error: Node execution failed: Unexpected error processing merge data request for request_id d501693b-b4ca-44eb-8a6c-cc72a1a71df8: "Required field \'main_input\' not found in parameters"', 'error_type': 'Exception'}
2025-06-26 16:50:50 - KafkaWorkflowConsumer - DEBUG - Stopped workflow with correlation_id: 4e28a473-fdf4-4bd6-988c-f666c08a7466 
2025-06-26 16:51:48 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:51:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:51:49 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:51:49 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:52:48 - RedisEventListener - DEBUG - Sending keep-alive to Redis connections
2025-06-26 16:52:48 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:52:49 - RedisEventListener - DEBUG - Sent keep-alive PING to Redis server at **************:6379, response: [b'pong', b'']
2025-06-26 16:52:49 - RedisEventListener - DEBUG - Keep-alive status: results=True, state=True
2025-06-26 16:53:06 - KafkaWorkflowConsumer - INFO - Consumer task cancelled.
2025-06-26 16:53:06 - AgentExecutor - INFO - Result consumer loop cancelled.
2025-06-26 16:53:06 - NodeExecutor - INFO - Result consumer loop cancelled.
2025-06-26 16:53:06 - MCPToolExecutor - INFO - Result consumer loop cancelled.
2025-06-26 16:53:06 - Main - ERROR - Shutting down due to keyboard interrupt...
